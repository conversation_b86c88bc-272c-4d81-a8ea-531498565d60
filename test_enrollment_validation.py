#!/usr/bin/env python3
"""
Test script to validate enrollment face detection
"""
import requests
import os
import tempfile
from PIL import Image, ImageDraw
import numpy as np

def create_test_image_with_multiple_faces():
    """Create a test image with multiple faces (circles to simulate faces)"""
    # Create a simple image with two circles (simulating faces)
    img = Image.new('RGB', (400, 300), color='white')
    draw = ImageDraw.Draw(img)
    
    # Draw two circles to simulate faces
    draw.ellipse([50, 50, 150, 150], fill='lightblue', outline='blue')
    draw.ellipse([250, 100, 350, 200], fill='lightpink', outline='red')
    
    # Save to temporary file
    with tempfile.NamedTemporaryFile(suffix='.jpg', delete=False) as tmp:
        img.save(tmp.name, 'JPEG')
        return tmp.name

def test_face_validation():
    """Test the face validation endpoint"""
    print("Testing face validation...")
    
    # Create test image with multiple "faces"
    test_image_path = create_test_image_with_multiple_faces()
    
    try:
        # Test the face check endpoint
        with open(test_image_path, 'rb') as f:
            files = {'file': f}
            response = requests.post('http://localhost:8551/check_face', files=files)
        
        if response.status_code == 200:
            result = response.json()
            print(f"Face validation result: {result}")
            
            if not result.get('success'):
                print("✅ Face validation correctly rejected multiple faces")
            else:
                print("❌ Face validation incorrectly accepted multiple faces")
        else:
            print(f"❌ Face validation endpoint error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error testing face validation: {e}")
    finally:
        # Clean up
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

def test_enrollment_with_multiple_faces():
    """Test full enrollment with multiple face image"""
    print("\nTesting enrollment with multiple faces...")
    
    test_image_path = create_test_image_with_multiple_faces()
    
    try:
        # Test enrollment
        data = {
            'name': 'Test Student',
            'family_name': 'Multiple Faces',
            'dob': '2000-01-01',
            'class_id': '1'
        }
        
        with open(test_image_path, 'rb') as f:
            files = {'files[]': f}
            response = requests.post('http://localhost:8551/', data=data, files=files)
        
        if response.status_code == 400:
            print("✅ Enrollment correctly rejected multiple faces")
        elif response.status_code == 200:
            print("❌ Enrollment incorrectly accepted multiple faces")
            print("Response content:", response.text[:200])
        else:
            print(f"❌ Enrollment error: {response.status_code}")
            print("Response content:", response.text[:200])
            
    except Exception as e:
        print(f"❌ Error testing enrollment: {e}")
    finally:
        # Clean up
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

if __name__ == "__main__":
    print("Starting enrollment validation tests...")
    test_face_validation()
    test_enrollment_with_multiple_faces()
    print("Tests completed.")
